syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "google/protobuf/wrappers.proto";

// ==================== 视频分类相关 ====================

// 视频分类信息
message VideoCategory {
  uint32 id = 1;                    // 分类ID
  string name = 2;                  // 分类名称（当前语言）
  string description = 3;           // 分类描述（当前语言）
  uint32 video_count = 4;           // 分类下视频数量
  string remark = 5;                // 备注
  int64 created_at = 6;             // 创建时间戳
  int64 updated_at = 7;             // 更新时间戳
}

// 视频分类列表请求
message VideoCategoryListReq {
  string name = 1;                  // 分类名称（可选，用于搜索）
  common.PageRequest page = 2;      // 分页参数
}

// 视频分类列表响应数据
message VideoCategoryListResData {
  repeated VideoCategory list = 1;  // 分类列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频分类列表响应
message VideoCategoryListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoCategoryListResData data = 4;
}

// ==================== 视频播放列表相关 ====================

// 视频播放列表信息
message VideoPlaylist {
  uint32 id = 1;                    // 播放列表ID
  string name = 2;                  // 播放列表名称（当前语言）
  string short_title = 3;           // 播放列表短标题（当前语言）
  string description = 4;           // 播放列表描述（当前语言）
  string cover_url = 5;             // 专题封面图片链接
  uint32 is_visible = 6;            // 是否显示，0-隐藏，1-显示
  uint32 sort_order = 7;            // 排序权重，数字越小越靠前
  uint32 video_count = 8;           // 播放列表下视频数量
  uint64 view_count = 9;            // 播放列表浏览次数
  uint64 share_count = 10;          // 播放列表分享次数
  uint64 collect_count = 11;        // 播放列表收藏次数
  int64 created_at = 12;            // 创建时间戳
  int64 updated_at = 13;            // 更新时间戳
}

// 视频播放列表列表请求
message VideoPlaylistListReq {
  string name = 1;                  // 播放列表名称（可选，用于搜索）
  google.protobuf.UInt32Value is_visible = 2; // 是否显示（可选）
  common.PageRequest page = 3;      // 分页参数
}

// 视频播放列表列表响应数据
message VideoPlaylistListResData {
  repeated VideoPlaylist list = 1;  // 播放列表列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频播放列表列表响应
message VideoPlaylistListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoPlaylistListResData data = 4;
}

// 视频播放列表详情请求
message VideoPlaylistDetailReq {
  uint32 playlist_id = 1;           // 播放列表ID
}

// 视频播放列表详情响应
message VideoPlaylistDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoPlaylist data = 4;
}

// ==================== 视频相关 ====================

// 视频信息
message Video {
  uint32 id = 1;                    // 视频ID
  uint32 category_id = 2;           // 分类ID
  string category_name = 3;         // 分类名称（当前语言）
  string title = 4;                 // 视频标题（当前语言）
  string description = 5;           // 视频描述（当前语言）
  string video_url = 6;             // 视频文件URL
  uint64 video_size = 7;            // 视频文件大小(字节)
  uint32 video_duration = 8;        // 视频时长(秒)
  string video_format = 9;          // 视频格式：mp4, mov等
  uint64 view_count = 10;           // 播放次数
  uint64 share_count = 11;          // 分享次数
  uint64 collect_count = 12;        // 收藏次数
  string creator_name = 13;         // 创建者姓名
  string author = 14;               // 视频作者
  string author_logo = 15;          // 作者头像URL
  uint32 author_auth_status = 16;   // 作者认证状态：0-未认证，1-已认证
  uint32 publish_state = 17;        // 发布状态：0-待发布，1-已发布，2-已下线
  uint32 is_recommended = 18;       // 是否推荐，0-否，1-是
  int64 created_at = 19;            // 创建时间戳
  int64 published_at = 20;          // 发布时间戳
  int64 updated_at = 21;            // 更新时间戳
  bool is_collected = 22;           // 当前用户是否已收藏（需要登录）
}

// 视频列表请求
message VideoListReq {
  google.protobuf.UInt32Value category_id = 1;     // 分类ID（可选）
  google.protobuf.UInt32Value playlist_id = 2;     // 播放列表ID（可选）
  string title = 3;                                 // 视频标题（可选，用于搜索）
  string author = 4;                                // 作者（可选，用于搜索）
  google.protobuf.UInt32Value publish_state = 5;   // 发布状态（可选）
  google.protobuf.UInt32Value is_recommended = 6;  // 是否推荐（可选）
  string sort_by = 7;                               // 排序方式：view_count, created_at, published_at
  string sort_order = 8;                            // 排序顺序：asc, desc
  common.PageRequest page = 9;                      // 分页参数
}

// 视频列表响应数据
message VideoListResData {
  repeated Video list = 1;          // 视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频列表响应
message VideoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoListResData data = 4;
}

// 视频详情请求
message VideoDetailReq {
  uint32 video_id = 1;              // 视频ID
}

// 视频详情响应
message VideoDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  Video data = 4;
}

// ==================== 视频收藏相关 ====================

// 视频收藏请求
message VideoCollectReq {
  uint32 video_id = 1;              // 视频ID
  uint32 is_add = 2;                // 是否添加收藏，1-添加，0-取消收藏
}

// 视频收藏响应
message VideoCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// 检查视频收藏状态请求
message CheckVideoCollectStatusReq {
  uint32 video_id = 1;              // 视频ID
}

// 检查视频收藏状态响应
message CheckVideoCollectStatusRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  bool is_collected = 4;            // 是否已收藏
}

// 视频收藏列表请求
message VideoCollectListReq {
  common.PageRequest page = 1;      // 分页参数
}

// 视频收藏列表响应数据
message VideoCollectListResData {
  repeated Video list = 1;          // 收藏的视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频收藏列表响应
message VideoCollectListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoCollectListResData data = 4;
}

// ==================== 视频分享相关 ====================

// 视频分享请求
message VideoShareReq {
  uint32 video_id = 1;              // 视频ID
  string share_platform = 2;        // 分享平台：wechat, facebook, twitter, whatsapp等
}

// 视频分享响应
message VideoShareRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// ==================== 视频播放历史相关 ====================

// 视频播放历史信息
message VideoPlayHistory {
  uint32 id = 1;                    // 历史记录ID
  uint32 video_id = 2;              // 视频ID
  Video video = 3;                  // 视频信息
  uint32 play_position = 4;         // 播放位置(秒)
  uint32 play_duration = 5;         // 本次播放时长(秒)
  uint32 is_completed = 6;          // 是否播放完成，0-否，1-是
  int64 created_at = 7;             // 播放时间戳
  int64 updated_at = 8;             // 更新时间戳
}

// 记录视频播放历史请求
message VideoPlayHistoryRecordReq {
  uint32 video_id = 1;              // 视频ID
  uint32 play_position = 2;         // 播放位置(秒)
  uint32 play_duration = 3;         // 本次播放时长(秒)
  uint32 is_completed = 4;          // 是否播放完成，0-否，1-是
}

// 记录视频播放历史响应
message VideoPlayHistoryRecordRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// 视频播放历史列表请求
message VideoPlayHistoryListReq {
  common.PageRequest page = 1;      // 分页参数
}

// 视频播放历史列表响应数据
message VideoPlayHistoryListResData {
  repeated VideoPlayHistory list = 1; // 播放历史列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频播放历史列表响应
message VideoPlayHistoryListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoPlayHistoryListResData data = 4;
}

// 获取视频播放进度请求
message VideoPlayProgressReq {
  uint32 video_id = 1;              // 视频ID
}

// 获取视频播放进度响应
message VideoPlayProgressRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  uint32 play_position = 4;         // 播放位置(秒)
  uint32 is_completed = 5;          // 是否播放完成，0-否，1-是
}

// ==================== 播放列表中的视频相关 ====================

// 播放列表中的视频列表请求
message PlaylistVideoListReq {
  uint32 playlist_id = 1;           // 播放列表ID
  common.PageRequest page = 2;      // 分页参数
}

// 播放列表中的视频列表响应数据
message PlaylistVideoListResData {
  repeated Video list = 1;          // 视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 播放列表中的视频列表响应
message PlaylistVideoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PlaylistVideoListResData data = 4;
}

// ==================== 推荐视频相关 ====================

// 推荐视频列表请求
message RecommendedVideoListReq {
  google.protobuf.UInt32Value category_id = 1; // 分类ID（可选）
  common.PageRequest page = 2;      // 分页参数
}

// 推荐视频列表响应数据
message RecommendedVideoListResData {
  repeated Video list = 1;          // 推荐视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 推荐视频列表响应
message RecommendedVideoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  RecommendedVideoListResData data = 4;
}

// ==================== 服务定义 ====================

service VideoService {
  // 视频分类相关
  rpc VideoCategoryList(VideoCategoryListReq) returns (VideoCategoryListRes);  // 视频分类列表

  // 视频播放列表相关
  rpc VideoPlaylistList(VideoPlaylistListReq) returns (VideoPlaylistListRes); // 视频播放列表列表
  rpc VideoPlaylistDetail(VideoPlaylistDetailReq) returns (VideoPlaylistDetailRes); // 视频播放列表详情
  rpc PlaylistVideoList(PlaylistVideoListReq) returns (PlaylistVideoListRes); // 播放列表中的视频列表

  // 视频相关
  rpc VideoList(VideoListReq) returns (VideoListRes);                         // 视频列表
  rpc VideoDetail(VideoDetailReq) returns (VideoDetailRes);                   // 视频详情
  rpc RecommendedVideoList(RecommendedVideoListReq) returns (RecommendedVideoListRes); // 推荐视频列表

  // 视频收藏相关
  rpc VideoCollect(VideoCollectReq) returns (VideoCollectRes);                // 视频收藏/取消收藏
  rpc CheckVideoCollectStatus(CheckVideoCollectStatusReq) returns (CheckVideoCollectStatusRes); // 检查视频收藏状态
  rpc VideoCollectList(VideoCollectListReq) returns (VideoCollectListRes);    // 视频收藏列表

  // 视频分享相关
  rpc VideoShare(VideoShareReq) returns (VideoShareRes);                      // 视频分享

  // 视频播放历史相关
  rpc VideoPlayHistoryRecord(VideoPlayHistoryRecordReq) returns (VideoPlayHistoryRecordRes); // 记录视频播放历史
  rpc VideoPlayHistoryList(VideoPlayHistoryListReq) returns (VideoPlayHistoryListRes); // 视频播放历史列表
  rpc VideoPlayProgress(VideoPlayProgressReq) returns (VideoPlayProgressRes); // 获取视频播放进度
}
