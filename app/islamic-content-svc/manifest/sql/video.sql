-- 视频分类表
CREATE TABLE `video_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `video_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类下视频数量',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';

-- 视频分类多语言表
CREATE TABLE `video_category_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_category_language` (`category_id`, `language_id`),
  INDEX `idx_language_id` (`language_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类多语言表';

-- 视频播放列表表
CREATE TABLE `video_playlists` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cover_url` varchar(500) NOT NULL DEFAULT '' COMMENT '专题封面图片链接',
  `is_visible` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否显示，0-隐藏，1-显示',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `video_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '播放列表下视频数量',
  `view_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '播放列表浏览次数',
  `share_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '播放列表分享次数',
  `collect_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '播放列表收藏次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_visible_sort` (`is_visible`, `sort_order`),
  INDEX `idx_view_count` (`view_count`),
  INDEX `idx_collect_count` (`collect_count`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放列表表';

-- 视频播放列表多语言表
CREATE TABLE `video_playlist_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `playlist_id` int(11) unsigned NOT NULL COMMENT '播放列表ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '播放列表名称',
  `short_title` varchar(100) NOT NULL DEFAULT '' COMMENT '播放列表短标题',
  `description` text COMMENT '播放列表描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_playlist_language` (`playlist_id`, `language_id`),
  INDEX `idx_language_id` (`language_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放列表多语言表';

-- 视频主表
CREATE TABLE `videos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `video_url` varchar(500) NOT NULL DEFAULT '' COMMENT '视频文件URL',
  `video_size` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '视频文件大小(字节)',
  `video_duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '视频时长(秒)',
  `video_format` varchar(20) NOT NULL DEFAULT '' COMMENT '视频格式：mp4, mov等',
  `view_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '播放次数',
  `share_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '分享次数',
  `collect_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者姓名',
  `author` varchar(100) NOT NULL DEFAULT '' COMMENT '视频作者',
  `author_logo` varchar(500) NOT NULL DEFAULT '' COMMENT '作者头像URL',
  `author_auth_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '作者认证状态：0-未认证，1-已认证',
  `publish_state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发布状态：0-待发布，1-已发布，2-已下线',
  `is_recommended` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐，0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_category_state_recommended` (`category_id`, `publish_state`, `is_recommended`),
  INDEX `idx_state_published_at` (`publish_state`, `published_at`),
  INDEX `idx_recommended_view_count` (`is_recommended`, `view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频主表';

-- 视频多语言表
CREATE TABLE `video_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '视频标题',
  `description` text COMMENT '视频描述(富文本)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_video_language` (`video_id`, `language_id`),
  INDEX `idx_language_id` (`language_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频多语言表';

-- 播放列表视频关联表
CREATE TABLE `video_playlist_relations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `playlist_id` int(11) unsigned NOT NULL COMMENT '播放列表ID',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_playlist_video` (`playlist_id`, `video_id`),
  INDEX `idx_playlist_sort` (`playlist_id`, `sort_order`),
  INDEX `idx_video_id` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='播放列表视频关联表';

-- 视频收藏表
CREATE TABLE `video_collects` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_user_video` (`user_id`, `video_id`),
  INDEX `idx_video_id` (`video_id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频收藏表';

-- 视频分享记录表
CREATE TABLE `video_shares` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，0表示未登录用户',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `share_platform` varchar(50) NOT NULL DEFAULT '' COMMENT '分享平台：wechat, facebook, twitter, whatsapp等',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
  PRIMARY KEY (`id`),
  INDEX `idx_video_id` (`video_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_video_time` (`video_id`, `created_at`),
  INDEX `idx_platform` (`share_platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分享记录表';


-- 视频播放历史表（用户个人播放记录）
CREATE TABLE `video_play_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `play_position` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '播放位置(秒)',
  `play_duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '本次播放时长(秒)',
  `is_completed` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否播放完成，0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '播放时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_user_video` (`user_id`, `video_id`),
  INDEX `idx_video_id` (`video_id`),
  INDEX `idx_user_time` (`user_id`, `created_at`),
  INDEX `idx_completed` (`is_completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放历史表';

